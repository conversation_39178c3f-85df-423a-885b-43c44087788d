# LocationV1 - Manual Network Database Setup

## 🎯 **Complete Setup Guide**

I've created everything you need for the network database setup. Follow these steps:

## 📋 **Step 1: Setup MySQL Database (Your PC)**

### **Option A: Using MySQL Workbench (Recommended)**
1. **Open MySQL Workbench**
2. **Connect to your local MySQL server**
3. **Open the SQL file:**
   - File → Open SQL Script
   - Select: `mysql-setup-commands.sql`
4. **Execute the script** (click the lightning bolt icon)
5. **Verify success** - you should see "Database setup completed successfully!"

### **Option B: Using MySQL Command Line**
1. **Open Command Prompt as Administrator**
2. **Navigate to your project folder:**
   ```cmd
   cd "C:\Users\<USER>\Desktop\LocationV12"
   ```
3. **Run MySQL with your root password:**
   ```cmd
   "C:\Program Files\MySQL\MySQL Server 8.0\bin\mysql.exe" -u root -p < mysql-setup-commands.sql
   ```
4. **Enter your MySQL root password when prompted**

## 📋 **Step 2: Configure Windows Firewall**

**Run as Administrator:**
```cmd
netsh advfirewall firewall add rule name="MySQL LocationV1" dir=in action=allow protocol=TCP localport=3306
```

## 📋 **Step 3: Find Your IP Address**

**Run this command:**
```cmd
ipconfig
```

**Look for "IPv4 Address" - example: ***************

## 📋 **Step 4: Test Your Server Setup**

**Test database connection:**
```cmd
"C:\Program Files\MySQL\MySQL Server 8.0\bin\mysql.exe" -u locationapp -plocation123 locationdb5 -e "SELECT 'Connection successful!' as Result;"
```

**If successful, you'll see: "Connection successful!"**

## 📋 **Step 5: Build and Test Your Application**

1. **Build your application:**
   ```cmd
   mvn clean package
   ```

2. **Create new executable:**
   ```cmd
   build-simple-fix.bat
   ```

3. **Test your application** - it should connect to the database

## 📋 **Step 6: Setup Client PCs (Other PCs)**

### **For Each Other PC:**

1. **Copy your entire project folder** to the other PC

2. **Create client configuration** - I'll create this for you:

**Replace their `src/main/resources/hibernate.cfg.xml` with this content:**
(Replace `YOUR_IP_ADDRESS` with your actual IP)

```xml
<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE hibernate-configuration PUBLIC
        "-//Hibernate/Hibernate Configuration DTD 3.0//EN"
        "http://hibernate.sourceforge.net/hibernate-configuration-3.0.dtd">
<hibernate-configuration>
    <session-factory>
        <property name="hibernate.connection.driver_class">com.mysql.cj.jdbc.Driver</property>
        <property name="hibernate.connection.url">****************************************************************************************************************************************************</property>
        <property name="hibernate.connection.username">locationapp</property>
        <property name="hibernate.connection.password">location123</property>
        <property name="hibernate.dialect">org.hibernate.dialect.MySQL8Dialect</property>
        <property name="hibernate.hbm2ddl.auto">update</property>
        <property name="show_sql">false</property>
        
        <!-- Connection Pool Settings -->
        <property name="hibernate.connection.pool_size">10</property>
        <property name="hibernate.connection.autocommit">false</property>
        <property name="hibernate.connection.isolation">2</property>
        
        <!-- Performance Settings -->
        <property name="hibernate.cache.use_second_level_cache">false</property>
        <property name="hibernate.cache.use_query_cache">false</property>
        
        <!-- Mapping des entités -->
        <mapping class="model.Vehicule"/>
        <mapping class="model.Client"/>
        <mapping class="model.Location"/>
        <mapping class="model.Paiement"/>
        <mapping class="model.Admin"/>
        <mapping class="model.Agent"/>
        <mapping class="model.VehicleMaintenance"/>
        <mapping class="model.VehicleFailure"/>
    </session-factory>
</hibernate-configuration>
```

3. **Rebuild on client PC:**
   ```cmd
   mvn clean package
   build-simple-fix.bat
   ```

## 🧪 **Step 7: Test the Network Setup**

1. **Start your application on your PC (server)**
2. **Add a test client or vehicle**
3. **Start application on other PC (client)**
4. **Verify the same data appears on both PCs**

## ✅ **Success Checklist**

### **Your PC (Server):**
- ✅ MySQL running
- ✅ Database `locationdb5` exists
- ✅ User `locationapp` can connect
- ✅ Firewall port 3306 open
- ✅ Application connects to database

### **Other PCs (Clients):**
- ✅ Can ping your IP address
- ✅ hibernate.cfg.xml points to your IP
- ✅ Application connects to your database
- ✅ Sees same data as your PC

## 🛠️ **Troubleshooting**

### **Database Connection Failed:**
- Check MySQL is running on your PC
- Verify the SQL commands were executed
- Test with: `mysql -u locationapp -plocation123 locationdb5`

### **Network Connection Failed:**
- Check both PCs are on same network
- Test with: `ping YOUR_IP_ADDRESS`
- Check Windows Firewall

### **Application Won't Start:**
- Check hibernate.cfg.xml has correct IP address
- Verify MySQL connector is in dependencies
- Check console for error messages

## 📞 **Quick Commands Reference**

```cmd
# Test database connection
mysql -u locationapp -plocation123 locationdb5 -e "SELECT 'Test' as Result;"

# Check firewall rule
netsh advfirewall firewall show rule name="MySQL LocationV1"

# Find IP address
ipconfig | findstr IPv4

# Test network connectivity
ping YOUR_IP_ADDRESS
```

## 🎉 **Final Result**

After setup:
- **Your PC:** MySQL server + LocationV1 app
- **Other PCs:** LocationV1 apps connecting to your database
- **All PCs:** Share same clients, vehicles, bookings, payments
- **Real-time:** Changes appear on all PCs instantly

**Everything is ready - just follow the steps above!** 🚀
