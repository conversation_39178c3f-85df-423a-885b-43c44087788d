@echo off
setlocal enabledelayedexpansion
echo ========================================
echo LocationV1 - CLIENT PC SETUP
echo ========================================

echo.
echo This will configure this PC to connect to the server database.
echo.

set /p server_ip="Enter the SERVER PC IP address: "
set /p test_connection="Test connection first? (Y/N): "

if /i "%test_connection%"=="Y" (
    echo.
    echo Testing connection to server...
    ping -n 1 %server_ip% >nul
    if !ERRORLEVEL! NEQ 0 (
        echo ❌ Cannot reach server IP: %server_ip%
        echo Please check:
        echo 1. Server PC is running
        echo 2. IP address is correct
        echo 3. Both PCs are on same network
        pause
        exit /b 1
    ) else (
        echo ✅ Server is reachable
    )
    
    echo.
    echo Testing MySQL connection...
    mysql -h %server_ip% -u locationapp -plocation123 -e "SELECT 'Connection successful!' as Result;" locationdb5 2>nul
    if !ERRORLEVEL! NEQ 0 (
        echo ❌ Cannot connect to MySQL on server
        echo Please check:
        echo 1. MySQL is running on server
        echo 2. Server setup was completed
        echo 3. Firewall allows port 3306
        pause
        exit /b 1
    ) else (
        echo ✅ MySQL connection successful
    )
)

echo.
echo Creating client hibernate configuration...

if exist "src\main\resources\hibernate.cfg.xml" (
    echo Backing up original hibernate.cfg.xml...
    copy "src\main\resources\hibernate.cfg.xml" "src\main\resources\hibernate.cfg.xml.backup" >nul
)

echo ^<?xml version='1.0' encoding='utf-8'?^> > src\main\resources\hibernate.cfg.xml
echo ^<!DOCTYPE hibernate-configuration PUBLIC >> src\main\resources\hibernate.cfg.xml
echo         "-//Hibernate/Hibernate Configuration DTD 3.0//EN" >> src\main\resources\hibernate.cfg.xml
echo         "http://hibernate.sourceforge.net/hibernate-configuration-3.0.dtd"^> >> src\main\resources\hibernate.cfg.xml
echo ^<hibernate-configuration^> >> src\main\resources\hibernate.cfg.xml
echo     ^<session-factory^> >> src\main\resources\hibernate.cfg.xml
echo         ^<property name="hibernate.connection.driver_class"^>com.mysql.cj.jdbc.Driver^</property^> >> src\main\resources\hibernate.cfg.xml
echo         ^<property name="hibernate.connection.url"^>***************************************************************************************************************************************************^</property^> >> src\main\resources\hibernate.cfg.xml
echo         ^<property name="hibernate.connection.username"^>locationapp^</property^> >> src\main\resources\hibernate.cfg.xml
echo         ^<property name="hibernate.connection.password"^>location123^</property^> >> src\main\resources\hibernate.cfg.xml
echo         ^<property name="hibernate.dialect"^>org.hibernate.dialect.MySQL8Dialect^</property^> >> src\main\resources\hibernate.cfg.xml
echo         ^<property name="hibernate.hbm2ddl.auto"^>update^</property^> >> src\main\resources\hibernate.cfg.xml
echo         ^<property name="show_sql"^>false^</property^> >> src\main\resources\hibernate.cfg.xml
echo         ^<property name="hibernate.connection.pool_size"^>10^</property^> >> src\main\resources\hibernate.cfg.xml
echo         ^<property name="hibernate.connection.autocommit"^>false^</property^> >> src\main\resources\hibernate.cfg.xml
echo         ^<property name="hibernate.connection.isolation"^>2^</property^> >> src\main\resources\hibernate.cfg.xml
echo         ^<property name="hibernate.cache.use_second_level_cache"^>false^</property^> >> src\main\resources\hibernate.cfg.xml
echo         ^<property name="hibernate.cache.use_query_cache"^>false^</property^> >> src\main\resources\hibernate.cfg.xml
echo         ^<!-- Mapping des entités --^> >> src\main\resources\hibernate.cfg.xml
echo         ^<mapping class="model.Vehicule"/^> >> src\main\resources\hibernate.cfg.xml
echo         ^<mapping class="model.Client"/^> >> src\main\resources\hibernate.cfg.xml
echo         ^<mapping class="model.Location"/^> >> src\main\resources\hibernate.cfg.xml
echo         ^<mapping class="model.Paiement"/^> >> src\main\resources\hibernate.cfg.xml
echo         ^<mapping class="model.Admin"/^> >> src\main\resources\hibernate.cfg.xml
echo         ^<mapping class="model.Agent"/^> >> src\main\resources\hibernate.cfg.xml
echo         ^<mapping class="model.VehicleMaintenance"/^> >> src\main\resources\hibernate.cfg.xml
echo         ^<mapping class="model.VehicleFailure"/^> >> src\main\resources\hibernate.cfg.xml
echo     ^</session-factory^> >> src\main\resources\hibernate.cfg.xml
echo ^</hibernate-configuration^> >> src\main\resources\hibernate.cfg.xml

echo.
echo Rebuilding application with new configuration...
echo.

echo Step 1: Cleaning previous build...
if exist "C:\Program Files\Maven\bin\mvn.cmd" (
    "C:\Program Files\Maven\bin\mvn.cmd" clean
) else if exist "C:\Program Files\Apache\Maven\bin\mvn.cmd" (
    "C:\Program Files\Apache\Maven\bin\mvn.cmd" clean
) else (
    echo Maven not found in standard locations. Trying PATH...
    mvn clean
)

echo.
echo Step 2: Building new JAR...
if exist "C:\Program Files\Maven\bin\mvn.cmd" (
    "C:\Program Files\Maven\bin\mvn.cmd" package -DskipTests
) else if exist "C:\Program Files\Apache\Maven\bin\mvn.cmd" (
    "C:\Program Files\Apache\Maven\bin\mvn.cmd" package -DskipTests
) else (
    mvn package -DskipTests
)

if !ERRORLEVEL! NEQ 0 (
    echo ❌ Build failed!
    echo Please check the Maven output above.
    pause
    exit /b 1
)

echo.
echo Step 3: Creating new executable...
call build-simple-fix.bat

echo.
echo ========================================
echo CLIENT PC SETUP COMPLETE! ✅
echo ========================================
echo.
echo 📋 CONFIGURATION:
echo ✅ Connected to server: %server_ip%
echo ✅ Database: locationdb5
echo ✅ User: locationapp
echo ✅ Application rebuilt
echo ✅ New executable created
echo.
echo 📁 FILES:
echo ✅ hibernate.cfg.xml updated (backup saved as .backup)
echo ✅ New JAR: target\LocationV1-1.0.0.jar
echo ✅ New EXE: target\final\LocationV1-1.0.0.exe
echo.
echo 🧪 TESTING:
echo Your application is now configured to connect to the server database.
echo Run the application to test the connection.
echo.
echo 📞 TROUBLESHOOTING:
echo If connection fails:
echo 1. Check server PC is running
echo 2. Check server IP: %server_ip%
echo 3. Check both PCs are on same network
echo 4. Check Windows Firewall on server PC
echo.
pause
