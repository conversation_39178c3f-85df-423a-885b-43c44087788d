@echo off
echo ========================================
echo LocationV1 - Simple Server Setup
echo ========================================

echo.
echo This will set up your PC as the database server.
echo.

echo Step 1: Checking MySQL...
if exist "C:\Program Files\MySQL\MySQL Server 8.0\bin\mysql.exe" (
    set MYSQL_PATH="C:\Program Files\MySQL\MySQL Server 8.0\bin\mysql.exe"
    echo ✅ MySQL found
) else (
    echo ❌ MySQL not found!
    echo.
    echo Please install MySQL Server first:
    echo 1. Go to: https://dev.mysql.com/downloads/installer/
    echo 2. Download MySQL Installer
    echo 3. Choose "Server only" installation
    echo 4. Set root password (remember it!)
    echo 5. Run this script again
    pause
    exit /b 1
)

echo.
echo Step 2: Database Setup Instructions
echo.
echo I'll show you the commands to run in MySQL.
echo Please open MySQL Workbench or MySQL Command Line and run these:
echo.
echo ----------------------------------------
echo CREATE DATABASE IF NOT EXISTS locationdb5;
echo CREATE USER IF NOT EXISTS 'locationapp'@'localhost' IDENTIFIED BY 'location123';
echo CREATE USER IF NOT EXISTS 'locationapp'@'%%' IDENTIFIED BY 'location123';
echo GRANT ALL PRIVILEGES ON locationdb5.* TO 'locationapp'@'localhost';
echo GRANT ALL PRIVILEGES ON locationdb5.* TO 'locationapp'@'%%';
echo FLUSH PRIVILEGES;
echo ----------------------------------------
echo.

echo Creating SQL file for you to run...
echo CREATE DATABASE IF NOT EXISTS locationdb5; > mysql-setup.sql
echo CREATE USER IF NOT EXISTS 'locationapp'@'localhost' IDENTIFIED BY 'location123'; >> mysql-setup.sql
echo CREATE USER IF NOT EXISTS 'locationapp'@'%%' IDENTIFIED BY 'location123'; >> mysql-setup.sql
echo GRANT ALL PRIVILEGES ON locationdb5.* TO 'locationapp'@'localhost'; >> mysql-setup.sql
echo GRANT ALL PRIVILEGES ON locationdb5.* TO 'locationapp'@'%%'; >> mysql-setup.sql
echo FLUSH PRIVILEGES; >> mysql-setup.sql

echo ✅ Created mysql-setup.sql file
echo.
echo You can run this file in MySQL Workbench:
echo 1. Open MySQL Workbench
echo 2. Connect to your local MySQL
echo 3. File → Open SQL Script → mysql-setup.sql
echo 4. Execute the script
echo.

set /p continue="Have you run the MySQL commands? (Y/N): "
if /i "%continue%" NEQ "Y" (
    echo Please run the MySQL commands first, then run this script again.
    pause
    exit /b 1
)

echo.
echo Step 3: Testing database connection...
%MYSQL_PATH% -u locationapp -plocation123 -e "SELECT 'Connection successful!' as Result;" locationdb5 2>nul
if %ERRORLEVEL% EQU 0 (
    echo ✅ Database connection successful!
) else (
    echo ❌ Database connection failed!
    echo Please check:
    echo 1. MySQL commands were executed correctly
    echo 2. User 'locationapp' was created
    echo 3. Password is 'location123'
    pause
    exit /b 1
)

echo.
echo Step 4: Configuring Windows Firewall...
netsh advfirewall firewall delete rule name="MySQL LocationV1" >nul 2>&1
netsh advfirewall firewall add rule name="MySQL LocationV1" dir=in action=allow protocol=TCP localport=3306
echo ✅ Firewall configured (port 3306 opened)

echo.
echo Step 5: Getting your IP address...
echo.
echo Your IP addresses:
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /c:"IPv4 Address"') do (
    set ip=%%a
    set ip=!ip: =!
    echo   !ip!
)

echo.
set /p main_ip="Enter your main IP address from above: "

echo.
echo Step 6: Creating client configuration template...
echo ^<?xml version='1.0' encoding='utf-8'?^> > hibernate-for-clients.cfg.xml
echo ^<!DOCTYPE hibernate-configuration PUBLIC >> hibernate-for-clients.cfg.xml
echo         "-//Hibernate/Hibernate Configuration DTD 3.0//EN" >> hibernate-for-clients.cfg.xml
echo         "http://hibernate.sourceforge.net/hibernate-configuration-3.0.dtd"^> >> hibernate-for-clients.cfg.xml
echo ^<hibernate-configuration^> >> hibernate-for-clients.cfg.xml
echo     ^<session-factory^> >> hibernate-for-clients.cfg.xml
echo         ^<property name="hibernate.connection.driver_class"^>com.mysql.cj.jdbc.Driver^</property^> >> hibernate-for-clients.cfg.xml
echo         ^<property name="hibernate.connection.url"^>*************************************************************************************************************************************************^</property^> >> hibernate-for-clients.cfg.xml
echo         ^<property name="hibernate.connection.username"^>locationapp^</property^> >> hibernate-for-clients.cfg.xml
echo         ^<property name="hibernate.connection.password"^>location123^</property^> >> hibernate-for-clients.cfg.xml
echo         ^<property name="hibernate.dialect"^>org.hibernate.dialect.MySQL8Dialect^</property^> >> hibernate-for-clients.cfg.xml
echo         ^<property name="hibernate.hbm2ddl.auto"^>update^</property^> >> hibernate-for-clients.cfg.xml
echo         ^<property name="show_sql"^>false^</property^> >> hibernate-for-clients.cfg.xml
echo         ^<property name="hibernate.connection.pool_size"^>10^</property^> >> hibernate-for-clients.cfg.xml
echo         ^<property name="hibernate.connection.autocommit"^>false^</property^> >> hibernate-for-clients.cfg.xml
echo         ^<property name="hibernate.connection.isolation"^>2^</property^> >> hibernate-for-clients.cfg.xml
echo         ^<property name="hibernate.cache.use_second_level_cache"^>false^</property^> >> hibernate-for-clients.cfg.xml
echo         ^<property name="hibernate.cache.use_query_cache"^>false^</property^> >> hibernate-for-clients.cfg.xml
echo         ^<!-- Mapping des entités --^> >> hibernate-for-clients.cfg.xml
echo         ^<mapping class="model.Vehicule"/^> >> hibernate-for-clients.cfg.xml
echo         ^<mapping class="model.Client"/^> >> hibernate-for-clients.cfg.xml
echo         ^<mapping class="model.Location"/^> >> hibernate-for-clients.cfg.xml
echo         ^<mapping class="model.Paiement"/^> >> hibernate-for-clients.cfg.xml
echo         ^<mapping class="model.Admin"/^> >> hibernate-for-clients.cfg.xml
echo         ^<mapping class="model.Agent"/^> >> hibernate-for-clients.cfg.xml
echo         ^<mapping class="model.VehicleMaintenance"/^> >> hibernate-for-clients.cfg.xml
echo         ^<mapping class="model.VehicleFailure"/^> >> hibernate-for-clients.cfg.xml
echo     ^</session-factory^> >> hibernate-for-clients.cfg.xml
echo ^</hibernate-configuration^> >> hibernate-for-clients.cfg.xml

echo.
echo Step 7: Testing your application...
echo.
echo Now test your LocationV1 application:
echo 1. Build your application: mvn clean package
echo 2. Run your application
echo 3. Check if it connects to the database
echo.

echo ========================================
echo SERVER SETUP COMPLETE! ✅
echo ========================================
echo.
echo 📋 SUMMARY:
echo ✅ Database 'locationdb5' created
echo ✅ User 'locationapp' created (password: location123)
echo ✅ Firewall configured (port 3306 open)
echo ✅ Your server IP: %main_ip%
echo.
echo 📁 FILES CREATED:
echo ✅ mysql-setup.sql (database setup commands)
echo ✅ hibernate-for-clients.cfg.xml (for other PCs)
echo.
echo 🔄 FOR OTHER PCs:
echo 1. Give them the file: hibernate-for-clients.cfg.xml
echo 2. Tell them your IP: %main_ip%
echo 3. They replace their hibernate.cfg.xml with hibernate-for-clients.cfg.xml
echo 4. They rebuild their application
echo.
echo 🧪 TEST:
echo Run your LocationV1 application now to test!
echo.
pause
