-- LocationV1 Database Setup Commands
-- Run these commands in MySQL Workbench or MySQL Command Line

-- Create the database
CREATE DATABASE IF NOT EXISTS locationdb5;

-- Create the application user with network access
CREATE USER IF NOT EXISTS 'locationapp'@'localhost' IDENTIFIED BY 'location123';
CREATE USER IF NOT EXISTS 'locationapp'@'%' IDENTIFIED BY 'location123';

-- Grant all privileges on the database
GRANT ALL PRIVILEGES ON locationdb5.* TO 'locationapp'@'localhost';
GRANT ALL PRIVILEGES ON locationdb5.* TO 'locationapp'@'%';

-- Apply the changes
FLUSH PRIVILEGES;

-- Test the setup
SELECT 'Database setup completed successfully!' as Status;

-- Show the created database
SHOW DATABASES LIKE 'locationdb5';

-- Show the created users
SELECT User, Host FROM mysql.user WHERE User = 'locationapp';
