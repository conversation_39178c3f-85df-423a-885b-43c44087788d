@echo off
echo ========================================
echo LocationV1 - COMPLETE SERVER SETUP
echo ========================================

echo.
echo This will set up your PC as the database server for all other PCs.
echo.

echo Step 1: Checking MySQL installation...
mysql --version >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo MySQL not found in PATH. Checking common locations...
    if exist "C:\Program Files\MySQL\MySQL Server 8.0\bin\mysql.exe" (
        set MYSQL_PATH="C:\Program Files\MySQL\MySQL Server 8.0\bin"
        echo Found MySQL at: %MYSQL_PATH%
    ) else (
        echo ERROR: MySQL not found!
        echo Please install MySQL Server first.
        echo Download from: https://dev.mysql.com/downloads/installer/
        pause
        exit /b 1
    )
) else (
    set MYSQL_PATH=mysql
    echo MySQL found in PATH.
)

echo.
echo Step 2: Creating database and user...
echo.
echo Creating SQL setup script...

echo CREATE DATABASE IF NOT EXISTS locationdb5; > setup-db.sql
echo CREATE USER IF NOT EXISTS 'locationapp'@'localhost' IDENTIFIED BY 'location123'; >> setup-db.sql
echo CREATE USER IF NOT EXISTS 'locationapp'@'%%' IDENTIFIED BY 'location123'; >> setup-db.sql
echo GRANT ALL PRIVILEGES ON locationdb5.* TO 'locationapp'@'localhost'; >> setup-db.sql
echo GRANT ALL PRIVILEGES ON locationdb5.* TO 'locationapp'@'%%'; >> setup-db.sql
echo FLUSH PRIVILEGES; >> setup-db.sql
echo SELECT 'Database setup complete!' as Status; >> setup-db.sql

echo.
echo Running database setup...
if defined MYSQL_PATH (
    %MYSQL_PATH%\mysql.exe -u root -p < setup-db.sql
) else (
    mysql -u root -p < setup-db.sql
)

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo Database setup failed. Please check:
    echo 1. MySQL is running
    echo 2. Root password is correct
    echo 3. You have admin privileges
    pause
    exit /b 1
)

echo.
echo Step 3: Configuring Windows Firewall...
echo Opening port 3306 for MySQL...
netsh advfirewall firewall delete rule name="MySQL LocationV1" >nul 2>&1
netsh advfirewall firewall add rule name="MySQL LocationV1" dir=in action=allow protocol=TCP localport=3306

echo.
echo Step 4: Testing database connection...
echo Testing local connection...
echo SELECT 'Connection test successful!' as Result; > test-connection.sql
if defined MYSQL_PATH (
    %MYSQL_PATH%\mysql.exe -u locationapp -plocation123 locationdb5 < test-connection.sql
) else (
    mysql -u locationapp -plocation123 locationdb5 < test-connection.sql
)

if %ERRORLEVEL% EQU 0 (
    echo ✅ Database connection test PASSED!
) else (
    echo ❌ Database connection test FAILED!
    echo Please check the setup.
)

echo.
echo Step 5: Getting your IP address...
echo.
echo Your IP addresses:
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /c:"IPv4 Address"') do (
    set ip=%%a
    set ip=!ip: =!
    echo   !ip!
)

echo.
echo Step 6: Creating client configuration template...
echo Creating template for other PCs...

set /p main_ip="Enter your main IP address from above: "

echo ^<?xml version='1.0' encoding='utf-8'?^> > hibernate-client-template.cfg.xml
echo ^<!DOCTYPE hibernate-configuration PUBLIC >> hibernate-client-template.cfg.xml
echo         "-//Hibernate/Hibernate Configuration DTD 3.0//EN" >> hibernate-client-template.cfg.xml
echo         "http://hibernate.sourceforge.net/hibernate-configuration-3.0.dtd"^> >> hibernate-client-template.cfg.xml
echo ^<hibernate-configuration^> >> hibernate-client-template.cfg.xml
echo     ^<session-factory^> >> hibernate-client-template.cfg.xml
echo         ^<property name="hibernate.connection.driver_class"^>com.mysql.cj.jdbc.Driver^</property^> >> hibernate-client-template.cfg.xml
echo         ^<property name="hibernate.connection.url"^>*************************************************************************************************************************************************^</property^> >> hibernate-client-template.cfg.xml
echo         ^<property name="hibernate.connection.username"^>locationapp^</property^> >> hibernate-client-template.cfg.xml
echo         ^<property name="hibernate.connection.password"^>location123^</property^> >> hibernate-client-template.cfg.xml
echo         ^<property name="hibernate.dialect"^>org.hibernate.dialect.MySQL8Dialect^</property^> >> hibernate-client-template.cfg.xml
echo         ^<property name="hibernate.hbm2ddl.auto"^>update^</property^> >> hibernate-client-template.cfg.xml
echo         ^<property name="show_sql"^>false^</property^> >> hibernate-client-template.cfg.xml
echo         ^<property name="hibernate.connection.pool_size"^>10^</property^> >> hibernate-client-template.cfg.xml
echo         ^<property name="hibernate.connection.autocommit"^>false^</property^> >> hibernate-client-template.cfg.xml
echo         ^<property name="hibernate.connection.isolation"^>2^</property^> >> hibernate-client-template.cfg.xml
echo         ^<property name="hibernate.cache.use_second_level_cache"^>false^</property^> >> hibernate-client-template.cfg.xml
echo         ^<property name="hibernate.cache.use_query_cache"^>false^</property^> >> hibernate-client-template.cfg.xml
echo         ^<!-- Mapping des entités --^> >> hibernate-client-template.cfg.xml
echo         ^<mapping class="model.Vehicule"/^> >> hibernate-client-template.cfg.xml
echo         ^<mapping class="model.Client"/^> >> hibernate-client-template.cfg.xml
echo         ^<mapping class="model.Location"/^> >> hibernate-client-template.cfg.xml
echo         ^<mapping class="model.Paiement"/^> >> hibernate-client-template.cfg.xml
echo         ^<mapping class="model.Admin"/^> >> hibernate-client-template.cfg.xml
echo         ^<mapping class="model.Agent"/^> >> hibernate-client-template.cfg.xml
echo         ^<mapping class="model.VehicleMaintenance"/^> >> hibernate-client-template.cfg.xml
echo         ^<mapping class="model.VehicleFailure"/^> >> hibernate-client-template.cfg.xml
echo     ^</session-factory^> >> hibernate-client-template.cfg.xml
echo ^</hibernate-configuration^> >> hibernate-client-template.cfg.xml

echo.
echo ========================================
echo SERVER SETUP COMPLETE! ✅
echo ========================================
echo.
echo Your PC is now configured as the database server.
echo.
echo 📋 SUMMARY:
echo ✅ Database 'locationdb5' created
echo ✅ User 'locationapp' created with network access
echo ✅ Firewall configured (port 3306 open)
echo ✅ Connection tested successfully
echo ✅ Your IP address: %main_ip%
echo ✅ Client template created
echo.
echo 📁 FILES CREATED:
echo - hibernate-client-template.cfg.xml (for other PCs)
echo - setup-db.sql (database setup script)
echo - test-connection.sql (connection test)
echo.
echo 🔄 NEXT STEPS:
echo 1. Keep this PC running when others use the app
echo 2. Give other PC users the file: hibernate-client-template.cfg.xml
echo 3. Tell them your IP address: %main_ip%
echo 4. They should replace their hibernate.cfg.xml with the template
echo.
echo 🧪 TEST YOUR SETUP:
echo Run your LocationV1 application now to test the database connection.
echo.

del setup-db.sql
del test-connection.sql

pause
