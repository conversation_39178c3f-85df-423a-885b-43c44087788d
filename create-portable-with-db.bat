@echo off
echo ========================================
echo Creating Portable LocationV1 with Database
echo ========================================

echo.
echo This will create a version that includes a portable database.
echo No MySQL installation required on target PC.

echo.
echo Step 1: Creating database setup script...
echo Creating database initialization script...

echo.
echo Step 2: We need to modify the application to use embedded database.
echo.
echo RECOMMENDED APPROACH:
echo 1. Switch to H2 Database (embedded, no installation needed)
echo 2. Or include MySQL Embedded Server
echo 3. Or create database installer
echo.

echo Current hibernate.cfg.xml configuration needs to be updated.
echo.

pause

echo.
echo Would you like me to:
echo 1. Switch to H2 embedded database (RECOMMENDED)
echo 2. Create MySQL portable installer
echo 3. Show current database configuration
echo.
set /p choice="Enter choice (1-3): "

if "%choice%"=="1" goto switch_h2
if "%choice%"=="2" goto mysql_portable
if "%choice%"=="3" goto show_config

:switch_h2
echo.
echo Switching to H2 embedded database...
echo This requires updating your pom.xml and hibernate.cfg.xml
echo H2 database will be embedded in the application - no separate installation needed.
goto end

:mysql_portable
echo.
echo Creating MySQL portable installer...
echo This will include MySQL server with your application.
goto end

:show_config
echo.
echo Showing current database configuration...
if exist "src\main\resources\hibernate.cfg.xml" (
    type "src\main\resources\hibernate.cfg.xml"
) else (
    echo hibernate.cfg.xml not found in expected location.
)
goto end

:end
echo.
pause
