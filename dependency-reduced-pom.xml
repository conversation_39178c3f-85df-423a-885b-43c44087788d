<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.example</groupId>
  <artifactId>LocationV1</artifactId>
  <version>1.0-SNAPSHOT</version>
  <build>
    <plugins>
      <plugin>
        <groupId>org.openjfx</groupId>
        <artifactId>javafx-maven-plugin</artifactId>
        <version>0.0.8</version>
        <configuration>
          <mainClass>${main.class}</mainClass>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-shade-plugin</artifactId>
        <version>3.4.1</version>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
            <configuration>
              <transformers>
                <transformer>
                  <mainClass>${main.class}</mainClass>
                </transformer>
              </transformers>
              <filters>
                <filter>
                  <artifact>*:*</artifact>
                  <excludes>
                    <exclude>META-INF/*.SF</exclude>
                    <exclude>META-INF/*.DSA</exclude>
                    <exclude>META-INF/*.RSA</exclude>
                  </excludes>
                </filter>
              </filters>
              <finalName>${app.name}-${app.version}</finalName>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.11.0</version>
        <configuration>
          <source>${maven.compiler.source}</source>
          <target>${maven.compiler.target}</target>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>exec-maven-plugin</artifactId>
        <version>3.1.0</version>
        <configuration>
          <mainClass>${main.class}</mainClass>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.panteleyev</groupId>
        <artifactId>jpackage-maven-plugin</artifactId>
        <version>1.6.0</version>
        <executions>
          <execution>
            <id>win</id>
            <phase>package</phase>
            <goals>
              <goal>jpackage</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <name>${app.name}</name>
          <appVersion>${app.version}</appVersion>
          <vendor>${app.vendor}</vendor>
          <destination>target/dist</destination>
          <mainJar>${app.name}-${app.version}.jar</mainJar>
          <mainClass>${main.class}</mainClass>
          <javaOptions>
            <option>-Dfile.encoding=UTF-8</option>
          </javaOptions>
          <type>EXE</type>
          <winDirChooser>true</winDirChooser>
          <winMenu>true</winMenu>
          <winShortcut>true</winShortcut>
          <description>${app.description}</description>
        </configuration>
      </plugin>
    </plugins>
  </build>
  <properties>
    <exec.mainClass>Launcher</exec.mainClass>
    <main.class>Launcher</main.class>
    <maven.compiler.source>17</maven.compiler.source>
    <app.version>1.0.0</app.version>
    <app.vendor>Your Company</app.vendor>
    <app.description>Location de voitures - Application de gestion</app.description>
    <maven.compiler.target>17</maven.compiler.target>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <javafx.version>22.0.1</javafx.version>
    <app.name>LocationV1</app.name>
  </properties>
</project>
