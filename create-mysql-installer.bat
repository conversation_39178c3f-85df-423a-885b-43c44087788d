@echo off
echo ========================================
echo LocationV1 - MySQL Auto-Installer Creator
echo ========================================

echo.
echo This creates an installer that automatically installs MySQL
echo and sets up your database on any Windows PC.

echo.
echo Step 1: Creating MySQL installer script...

echo @echo off > mysql-auto-install.bat
echo echo ======================================== >> mysql-auto-install.bat
echo echo LocationV1 - MySQL Setup >> mysql-auto-install.bat
echo echo ======================================== >> mysql-auto-install.bat
echo echo. >> mysql-auto-install.bat
echo echo This will install MySQL Server and setup the database. >> mysql-auto-install.bat
echo echo. >> mysql-auto-install.bat
echo echo Step 1: Downloading MySQL Installer... >> mysql-auto-install.bat
echo powershell -Command "Invoke-WebRequest -Uri 'https://dev.mysql.com/get/Downloads/MySQLInstaller/mysql-installer-community-********.msi' -OutFile 'mysql-installer.msi'" >> mysql-auto-install.bat
echo echo. >> mysql-auto-install.bat
echo echo Step 2: Installing MySQL Server... >> mysql-auto-install.bat
echo echo Please follow the installer: >> mysql-auto-install.bat
echo echo 1. Choose "Server only" >> mysql-auto-install.bat
echo echo 2. Use default settings >> mysql-auto-install.bat
echo echo 3. Set root password as empty ^(just press Next^) >> mysql-auto-install.bat
echo echo 4. Complete the installation >> mysql-auto-install.bat
echo echo. >> mysql-auto-install.bat
echo msiexec /i mysql-installer.msi /quiet >> mysql-auto-install.bat
echo echo. >> mysql-auto-install.bat
echo echo Step 3: Starting MySQL Service... >> mysql-auto-install.bat
echo net start MySQL80 >> mysql-auto-install.bat
echo echo. >> mysql-auto-install.bat
echo echo Step 4: Creating database... >> mysql-auto-install.bat
echo "C:\Program Files\MySQL\MySQL Server 8.0\bin\mysql.exe" -u root -e "CREATE DATABASE IF NOT EXISTS locationdb5;" >> mysql-auto-install.bat
echo echo. >> mysql-auto-install.bat
echo echo MySQL setup complete! >> mysql-auto-install.bat
echo echo You can now run LocationV1. >> mysql-auto-install.bat
echo pause >> mysql-auto-install.bat

echo.
echo Step 2: Creating combined installer...

echo @echo off > install-locationv1-complete.bat
echo echo ======================================== >> install-locationv1-complete.bat
echo echo LocationV1 - Complete Installation >> install-locationv1-complete.bat
echo echo ======================================== >> install-locationv1-complete.bat
echo echo. >> install-locationv1-complete.bat
echo echo This will install: >> install-locationv1-complete.bat
echo echo 1. MySQL Server >> install-locationv1-complete.bat
echo echo 2. LocationV1 Application >> install-locationv1-complete.bat
echo echo. >> install-locationv1-complete.bat
echo set /p continue="Continue? (Y/N): " >> install-locationv1-complete.bat
echo if /i "%%continue%%" NEQ "Y" exit /b >> install-locationv1-complete.bat
echo echo. >> install-locationv1-complete.bat
echo echo Installing MySQL... >> install-locationv1-complete.bat
echo call mysql-auto-install.bat >> install-locationv1-complete.bat
echo echo. >> install-locationv1-complete.bat
echo echo Installing LocationV1... >> install-locationv1-complete.bat
echo if exist "target\final\LocationV1-1.0.0.exe" ( >> install-locationv1-complete.bat
echo     target\final\LocationV1-1.0.0.exe >> install-locationv1-complete.bat
echo ^) else ( >> install-locationv1-complete.bat
echo     echo LocationV1 executable not found! >> install-locationv1-complete.bat
echo     echo Please build the application first. >> install-locationv1-complete.bat
echo ^) >> install-locationv1-complete.bat
echo echo. >> install-locationv1-complete.bat
echo echo Installation complete! >> install-locationv1-complete.bat
echo pause >> install-locationv1-complete.bat

echo.
echo ========================================
echo MySQL Auto-Installer Created!
echo ========================================
echo.
echo Files created:
echo 1. mysql-auto-install.bat - Installs MySQL only
echo 2. install-locationv1-complete.bat - Installs everything
echo.
echo Usage:
echo - Give users: install-locationv1-complete.bat
echo - It will automatically install MySQL and your app
echo.
pause
