<?xml version='1.0' encoding='utf-8'?>
<!DOCTYPE hibernate-configuration PUBLIC
        "-//Hibernate/Hibernate Configuration DTD 3.0//EN"
        "http://hibernate.sourceforge.net/hibernate-configuration-3.0.dtd">
<hibernate-configuration>
    <session-factory>
        <!-- H2 Database Configuration (Embedded) -->
        <property name="hibernate.connection.driver_class">org.h2.Driver</property>
        <property name="hibernate.connection.url">jdbc:h2:file:./data/locationdb;AUTO_SERVER=TRUE;DB_CLOSE_ON_EXIT=FALSE</property>
        <property name="hibernate.connection.username">sa</property>
        <property name="hibernate.connection.password"></property>
        <property name="hibernate.dialect">org.hibernate.dialect.H2Dialect</property>
        <property name="hibernate.hbm2ddl.auto">update</property>
        <property name="show_sql">false</property>
        
        <!-- Connection Pool Settings -->
        <property name="hibernate.connection.pool_size">10</property>
        <property name="hibernate.connection.autocommit">false</property>
        
        <!-- Mapping des entités -->
        <mapping class="model.Vehicule"/>
        <mapping class="model.Client"/>
        <mapping class="model.Location"/>
        <mapping class="model.Paiement"/>
        <mapping class="model.Admin"/>
        <mapping class="model.Agent"/>
        <mapping class="model.VehicleMaintenance"/>
        <mapping class="model.VehicleFailure"/>
    </session-factory>
</hibernate-configuration>
