module LocationV1 {
    requires javafx.controls;
    requires javafx.fxml;
    requires javafx.swing;
    requires java.desktop;
    requires java.sql;
    requires org.hibernate.orm.core;
    requires mysql.connector.java;
    requires org.apache.pdfbox;
    requires jbcrypt;
    
    exports controller;
    exports model;
    exports dao;
    exports util;
    
    opens controller to javafx.fxml;
    opens model to org.hibernate.orm.core;
    opens view to javafx.fxml;
}
