@echo off
echo ========================================
echo LocationV1 - Build and Test
echo ========================================

:menu
echo.
echo Choose an option:
echo 1. Run application (test Launcher)
echo 2. Build JAR file
echo 3. Build Windows executable (.exe)
echo 4. Build and run JAR
echo 5. Exit
echo.
set /p choice="Enter your choice (1-5): "

if "%choice%"=="1" goto run_app
if "%choice%"=="2" goto build_jar
if "%choice%"=="3" goto build_exe
if "%choice%"=="4" goto build_and_run
if "%choice%"=="5" goto exit
goto menu

:run_app
echo.
echo Running application with Launcher...
call mvn compile exec:java
goto menu

:build_jar
echo.
echo Building JAR file...
call mvn clean package
echo.
echo JAR created: target\LocationV1-1.0.0.jar
echo You can run it with: java -jar target\LocationV1-1.0.0.jar
goto menu

:build_exe
echo.
echo Building Windows executable...
call build-exe-no-modules.bat
goto menu

:build_and_run
echo.
echo Building and running JAR...
call mvn clean package
if exist target\LocationV1-1.0.0.jar (
    echo.
    echo Running the JAR file...
    java -jar target\LocationV1-1.0.0.jar
) else (
    echo Build failed!
)
goto menu

:exit
echo.
echo Goodbye!
pause
exit
