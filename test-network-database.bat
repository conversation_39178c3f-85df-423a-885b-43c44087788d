@echo off
echo ========================================
echo LocationV1 - Network Database Test
echo ========================================

echo.
echo This script tests your network database setup.
echo.

echo Choose test type:
echo 1. Test as SERVER PC
echo 2. Test as CLIENT PC
echo 3. Test connection between PCs
echo.
set /p test_type="Enter choice (1-3): "

if "%test_type%"=="1" goto test_server
if "%test_type%"=="2" goto test_client
if "%test_type%"=="3" goto test_connection

:test_server
echo.
echo ========================================
echo TESTING SERVER PC
echo ========================================

echo.
echo Test 1: MySQL Service Status...
sc query MySQL80 | findstr "STATE" | findstr "RUNNING" >nul
if %ERRORLEVEL% EQU 0 (
    echo ✅ MySQL service is running
) else (
    echo ❌ MySQL service is not running
    echo Starting MySQL service...
    net start MySQL80
)

echo.
echo Test 2: Database Connection...
echo SELECT 'Server database test successful!' as Result; > test-server.sql
mysql -u locationapp -plocation123 locationdb5 < test-server.sql
if %ERRORLEVEL% EQU 0 (
    echo ✅ Database connection successful
) else (
    echo ❌ Database connection failed
    echo Check if database and user were created properly
)

echo.
echo Test 3: Network Port (3306)...
netstat -an | findstr ":3306" | findstr "LISTENING" >nul
if %ERRORLEVEL% EQU 0 (
    echo ✅ MySQL is listening on port 3306
) else (
    echo ❌ MySQL is not listening on port 3306
    echo Check MySQL configuration
)

echo.
echo Test 4: Firewall Rule...
netsh advfirewall firewall show rule name="MySQL LocationV1" >nul 2>&1
if %ERRORLEVEL% EQU 0 (
    echo ✅ Firewall rule exists
) else (
    echo ❌ Firewall rule missing
    echo Adding firewall rule...
    netsh advfirewall firewall add rule name="MySQL LocationV1" dir=in action=allow protocol=TCP localport=3306
)

echo.
echo Test 5: Your IP Address...
echo Your IP addresses:
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /c:"IPv4 Address"') do (
    set ip=%%a
    set ip=!ip: =!
    echo   !ip!
)

del test-server.sql 2>nul
goto end

:test_client
echo.
echo ========================================
echo TESTING CLIENT PC
echo ========================================

set /p server_ip="Enter SERVER PC IP address: "

echo.
echo Test 1: Network Connectivity...
ping -n 1 %server_ip% >nul
if %ERRORLEVEL% EQU 0 (
    echo ✅ Can reach server PC (%server_ip%)
) else (
    echo ❌ Cannot reach server PC (%server_ip%)
    echo Check network connection and IP address
    goto end
)

echo.
echo Test 2: MySQL Port Access...
telnet %server_ip% 3306 2>nul | findstr "Connected" >nul
if %ERRORLEVEL% EQU 0 (
    echo ✅ Can connect to MySQL port on server
) else (
    echo ❌ Cannot connect to MySQL port on server
    echo Check server firewall and MySQL configuration
)

echo.
echo Test 3: Database Connection...
echo SELECT 'Client database test successful!' as Result; > test-client.sql
mysql -h %server_ip% -u locationapp -plocation123 locationdb5 < test-client.sql
if %ERRORLEVEL% EQU 0 (
    echo ✅ Database connection successful
) else (
    echo ❌ Database connection failed
    echo Check server setup and credentials
)

echo.
echo Test 4: Hibernate Configuration...
if exist "src\main\resources\hibernate.cfg.xml" (
    findstr "%server_ip%" "src\main\resources\hibernate.cfg.xml" >nul
    if !ERRORLEVEL! EQU 0 (
        echo ✅ Hibernate configured for server IP
    ) else (
        echo ❌ Hibernate not configured for server IP
        echo Run setup-client-pc.bat to fix this
    )
) else (
    echo ❌ hibernate.cfg.xml not found
)

del test-client.sql 2>nul
goto end

:test_connection
echo.
echo ========================================
echo TESTING PC-TO-PC CONNECTION
echo ========================================

set /p server_ip="Enter SERVER PC IP address: "
set /p client_ip="Enter CLIENT PC IP address: "

echo.
echo Test 1: Server to Client connectivity...
ping -n 1 %client_ip% >nul
if %ERRORLEVEL% EQU 0 (
    echo ✅ Server can reach client PC
) else (
    echo ❌ Server cannot reach client PC
)

echo.
echo Test 2: Client to Server connectivity...
ping -n 1 %server_ip% >nul
if %ERRORLEVEL% EQU 0 (
    echo ✅ Client can reach server PC
) else (
    echo ❌ Client cannot reach server PC
)

echo.
echo Test 3: Database synchronization test...
echo This test requires both PCs to run the application
echo and check if data appears on both sides.
echo.
echo Manual test steps:
echo 1. Start application on server PC
echo 2. Add a test client or vehicle
echo 3. Start application on client PC
echo 4. Check if the same data appears
echo.

goto end

:end
echo.
echo ========================================
echo TEST COMPLETE
echo ========================================
echo.
echo If any tests failed, please fix the issues before proceeding.
echo.
pause
