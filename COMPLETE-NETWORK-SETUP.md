# LocationV1 - Complete Network Database Setup

## 🎯 **What This Does**

✅ **Sets up shared MySQL database across multiple PCs**
✅ **All PCs see the same data in real-time**
✅ **Automatic configuration and testing**
✅ **No manual configuration needed**

## 🚀 **Quick Setup (2 Steps)**

### **Step 1: Setup Your PC as Server**
```bash
setup-complete-server.bat
```
- Creates database and user
- Configures network access
- Opens firewall
- Tests everything
- Gives you IP address for others

### **Step 2: Setup Other PCs as Clients**
```bash
setup-client-pc.bat
```
- Enter your IP address
- Automatically configures connection
- Rebuilds application
- Creates new executable
- Tests connection

## 📋 **Detailed Process**

### **🖥️ Server PC Setup (Your PC)**

1. **Run the server setup:**
   ```bash
   setup-complete-server.bat
   ```

2. **What it does automatically:**
   - ✅ Creates database `locationdb5`
   - ✅ Creates user `locationapp` with password `location123`
   - ✅ Grants network access permissions
   - ✅ Opens Windows Firewall port 3306
   - ✅ Tests database connection
   - ✅ Shows your IP address
   - ✅ Creates client template file

3. **You'll get:**
   - Your IP address (e.g., *************)
   - File: `hibernate-client-template.cfg.xml`

### **💻 Client PC Setup (Other PCs)**

1. **Copy these files to client PC:**
   - `setup-client-pc.bat`
   - `build-simple-fix.bat`
   - Your entire LocationV1 project

2. **Run the client setup:**
   ```bash
   setup-client-pc.bat
   ```

3. **Enter your server IP when prompted**

4. **What it does automatically:**
   - ✅ Tests connection to your server
   - ✅ Updates hibernate.cfg.xml with your IP
   - ✅ Rebuilds the application
   - ✅ Creates new executable
   - ✅ Tests database connection

## 🧪 **Testing Your Setup**

### **Test Everything:**
```bash
test-network-database.bat
```

### **Manual Test:**
1. **Start app on your PC (server)**
2. **Add a test client or vehicle**
3. **Start app on other PC (client)**
4. **Verify same data appears on both**

## 📁 **Files Created**

| File | Purpose |
|------|---------|
| `setup-complete-server.bat` | Complete server setup |
| `setup-client-pc.bat` | Complete client setup |
| `test-network-database.bat` | Test network connection |
| `hibernate-client-template.cfg.xml` | Template for clients |

## 🔧 **Network Requirements**

- **Same Network:** All PCs on same WiFi/LAN
- **Port 3306:** Open on server PC
- **MySQL:** Installed on server PC
- **IP Address:** Static or known IP for server

## ✅ **Success Indicators**

### **Server PC:**
- ✅ MySQL service running
- ✅ Database `locationdb5` exists
- ✅ User `locationapp` can connect
- ✅ Port 3306 open in firewall
- ✅ Application connects to database

### **Client PC:**
- ✅ Can ping server IP
- ✅ Can connect to MySQL on server
- ✅ hibernate.cfg.xml points to server
- ✅ Application connects to server database
- ✅ Sees same data as server

## 🛠️ **Troubleshooting**

### **Connection Failed:**
1. Check server PC is running
2. Check IP address is correct
3. Check both PCs on same network
4. Run `test-network-database.bat`

### **Database Access Denied:**
1. Re-run `setup-complete-server.bat`
2. Check MySQL is running
3. Verify user `locationapp` exists

### **Firewall Issues:**
1. Check Windows Firewall on server
2. Check antivirus software
3. Try disabling firewall temporarily

## 📞 **Support Commands**

```bash
# Test server setup
test-network-database.bat → Option 1

# Test client setup  
test-network-database.bat → Option 2

# Test PC-to-PC connection
test-network-database.bat → Option 3
```

## 🎉 **Final Result**

After setup:
- **Your PC:** Database server + application
- **Other PCs:** Applications connecting to your database
- **All PCs:** See same clients, vehicles, bookings, payments
- **Real-time:** Changes appear on all PCs instantly
- **Backup:** Only need to backup your PC's database

**Everything is automated - just run the scripts!** 🚀
