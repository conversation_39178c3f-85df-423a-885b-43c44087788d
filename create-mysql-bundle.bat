@echo off
echo ========================================
echo Creating LocationV1 with Bundled MySQL
echo ========================================

echo.
echo This will create an installer that includes:
echo 1. Your LocationV1 application
echo 2. MySQL Server (portable)
echo 3. Automatic database setup
echo.

echo Step 1: Download MySQL Portable...
echo.
echo You need to download MySQL Portable from:
echo https://dev.mysql.com/downloads/mysql/
echo.
echo Choose: "Windows (x86, 64-bit), ZIP Archive"
echo.
echo After download:
echo 1. Extract to: mysql-portable\
echo 2. Run this script again
echo.

if not exist "mysql-portable" (
    echo MySQL Portable not found!
    echo.
    echo Please:
    echo 1. Download MySQL ZIP from MySQL website
    echo 2. Extract to folder named "mysql-portable"
    echo 3. Run this script again
    echo.
    pause
    exit /b 1
)

echo.
echo Step 2: Creating MySQL configuration...
echo Creating my.ini for portable MySQL...

echo [mysqld] > mysql-portable\my.ini
echo port=3306 >> mysql-portable\my.ini
echo basedir=.\ >> mysql-portable\my.ini
echo datadir=.\data >> mysql-portable\my.ini
echo default-storage-engine=INNODB >> mysql-portable\my.ini
echo sql-mode="STRICT_TRANS_TABLES,NO_AUTO_CREATE_USER,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO" >> mysql-portable\my.ini
echo max_connections=100 >> mysql-portable\my.ini
echo query_cache_size=0 >> mysql-portable\my.ini
echo table_open_cache=2000 >> mysql-portable\my.ini
echo tmp_table_size=16M >> mysql-portable\my.ini
echo thread_cache_size=10 >> mysql-portable\my.ini
echo myisam_max_sort_file_size=100G >> mysql-portable\my.ini
echo myisam_sort_buffer_size=8M >> mysql-portable\my.ini
echo key_buffer_size=8M >> mysql-portable\my.ini
echo read_buffer_size=0 >> mysql-portable\my.ini
echo read_rnd_buffer_size=0 >> mysql-portable\my.ini
echo bulk_insert_buffer_size=0 >> mysql-portable\my.ini

echo.
echo Step 3: Creating database initialization script...
echo Creating database setup script...

echo @echo off > setup-database.bat
echo echo Setting up LocationV1 Database... >> setup-database.bat
echo cd mysql-portable >> setup-database.bat
echo if not exist data mkdir data >> setup-database.bat
echo echo Initializing MySQL... >> setup-database.bat
echo bin\mysqld --initialize-insecure --console >> setup-database.bat
echo echo Starting MySQL Server... >> setup-database.bat
echo start /b bin\mysqld --console >> setup-database.bat
echo timeout /t 5 >> setup-database.bat
echo echo Creating database... >> setup-database.bat
echo bin\mysql -u root -e "CREATE DATABASE IF NOT EXISTS locationdb5;" >> setup-database.bat
echo echo Database setup complete! >> setup-database.bat
echo cd .. >> setup-database.bat

echo.
echo Step 4: Creating application launcher...
echo Creating launcher that starts MySQL and your app...

echo @echo off > start-locationv1.bat
echo echo Starting LocationV1 with MySQL... >> start-locationv1.bat
echo cd mysql-portable >> start-locationv1.bat
echo echo Starting MySQL Server... >> start-locationv1.bat
echo start /b bin\mysqld --console >> start-locationv1.bat
echo cd .. >> start-locationv1.bat
echo timeout /t 3 >> start-locationv1.bat
echo echo Starting LocationV1 Application... >> start-locationv1.bat
echo java -jar LocationV1-1.0.0.jar >> start-locationv1.bat

echo.
echo ========================================
echo MySQL Bundle Created!
echo ========================================
echo.
echo Files created:
echo - mysql-portable\my.ini (MySQL configuration)
echo - setup-database.bat (Database initialization)
echo - start-locationv1.bat (Application launcher)
echo.
echo Next steps:
echo 1. Run setup-database.bat (first time only)
echo 2. Use start-locationv1.bat to run your application
echo.
pause
