@echo off
echo ========================================
echo LocationV1 - Network Database Setup
echo ========================================

echo.
echo This helps you set up a shared MySQL database that all PCs can access.
echo.

echo Choose your setup:
echo 1. Setup SERVER PC (main database)
echo 2. Setup CLIENT PC (connects to server)
echo 3. Create network configuration files
echo.
set /p choice="Enter choice (1-3): "

if "%choice%"=="1" goto setup_server
if "%choice%"=="2" goto setup_client
if "%choice%"=="3" goto create_configs

:setup_server
echo.
echo ========================================
echo Setting up SERVER PC
echo ========================================
echo.
echo This PC will host the MySQL database for all other PCs.
echo.
echo Step 1: Install MySQL Server
echo - Download MySQL from: https://dev.mysql.com/downloads/installer/
echo - Choose "Server only" installation
echo - Set root password (remember it!)
echo - Allow network connections
echo.
echo Step 2: Configure MySQL for network access
echo.
echo Creating MySQL network configuration...

echo [mysqld] > my-network.cnf
echo bind-address = 0.0.0.0 >> my-network.cnf
echo port = 3306 >> my-network.cnf
echo max_connections = 200 >> my-network.cnf

echo.
echo Step 3: Create database and user
echo.
echo Run these commands in MySQL:
echo.
echo CREATE DATABASE locationdb5;
echo CREATE USER 'locationapp'@'%%' IDENTIFIED BY 'location123';
echo GRANT ALL PRIVILEGES ON locationdb5.* TO 'locationapp'@'%%';
echo FLUSH PRIVILEGES;
echo.
echo Step 4: Configure Windows Firewall
echo netsh advfirewall firewall add rule name="MySQL" dir=in action=allow protocol=TCP localport=3306
echo.
echo Your server IP address:
ipconfig | findstr IPv4
echo.
echo Give this IP address to client PCs.
goto end

:setup_client
echo.
echo ========================================
echo Setting up CLIENT PC
echo ========================================
echo.
echo This PC will connect to the server database.
echo.
set /p server_ip="Enter SERVER PC IP address: "
echo.
echo Creating client configuration...

echo ^<?xml version='1.0' encoding='utf-8'?^> > hibernate-client.cfg.xml
echo ^<!DOCTYPE hibernate-configuration PUBLIC >> hibernate-client.cfg.xml
echo         "-//Hibernate/Hibernate Configuration DTD 3.0//EN" >> hibernate-client.cfg.xml
echo         "http://hibernate.sourceforge.net/hibernate-configuration-3.0.dtd"^> >> hibernate-client.cfg.xml
echo ^<hibernate-configuration^> >> hibernate-client.cfg.xml
echo     ^<session-factory^> >> hibernate-client.cfg.xml
echo         ^<property name="hibernate.connection.driver_class"^>com.mysql.cj.jdbc.Driver^</property^> >> hibernate-client.cfg.xml
echo         ^<property name="hibernate.connection.url"^>*****************************************^</property^> >> hibernate-client.cfg.xml
echo         ^<property name="hibernate.connection.username"^>locationapp^</property^> >> hibernate-client.cfg.xml
echo         ^<property name="hibernate.connection.password"^>location123^</property^> >> hibernate-client.cfg.xml
echo         ^<property name="hibernate.dialect"^>org.hibernate.dialect.MySQL8Dialect^</property^> >> hibernate-client.cfg.xml
echo         ^<property name="hibernate.hbm2ddl.auto"^>update^</property^> >> hibernate-client.cfg.xml
echo         ^<property name="show_sql"^>false^</property^> >> hibernate-client.cfg.xml
echo         ^<!-- Mapping des entités --^> >> hibernate-client.cfg.xml
echo         ^<mapping class="model.Vehicule"/^> >> hibernate-client.cfg.xml
echo         ^<mapping class="model.Client"/^> >> hibernate-client.cfg.xml
echo         ^<mapping class="model.Location"/^> >> hibernate-client.cfg.xml
echo         ^<mapping class="model.Paiement"/^> >> hibernate-client.cfg.xml
echo         ^<mapping class="model.Admin"/^> >> hibernate-client.cfg.xml
echo         ^<mapping class="model.Agent"/^> >> hibernate-client.cfg.xml
echo         ^<mapping class="model.VehicleMaintenance"/^> >> hibernate-client.cfg.xml
echo         ^<mapping class="model.VehicleFailure"/^> >> hibernate-client.cfg.xml
echo     ^</session-factory^> >> hibernate-client.cfg.xml
echo ^</hibernate-configuration^> >> hibernate-client.cfg.xml

echo.
echo Client configuration created: hibernate-client.cfg.xml
echo.
echo Replace your hibernate.cfg.xml with this file.
goto end

:create_configs
echo.
echo Creating network configuration files...
echo.
echo This creates configuration files for different network setups.
echo.

echo Creating server setup script...
echo @echo off > setup-mysql-server.bat
echo echo Setting up MySQL Server for network access... >> setup-mysql-server.bat
echo echo. >> setup-mysql-server.bat
echo echo 1. Install MySQL Server >> setup-mysql-server.bat
echo echo 2. Configure for network access >> setup-mysql-server.bat
echo echo 3. Create database and user >> setup-mysql-server.bat
echo echo. >> setup-mysql-server.bat
echo mysql -u root -p -e "CREATE DATABASE IF NOT EXISTS locationdb5; CREATE USER IF NOT EXISTS 'locationapp'@'%%' IDENTIFIED BY 'location123'; GRANT ALL PRIVILEGES ON locationdb5.* TO 'locationapp'@'%%'; FLUSH PRIVILEGES;" >> setup-mysql-server.bat

echo.
echo Files created:
echo - setup-mysql-server.bat (for server PC)
echo - hibernate-client.cfg.xml (for client PCs)
goto end

:end
echo.
echo ========================================
echo Network Database Setup Complete!
echo ========================================
echo.
echo Summary:
echo - SERVER PC: Hosts MySQL database
echo - CLIENT PCs: Connect to server database
echo - All PCs share the same data
echo.
pause
